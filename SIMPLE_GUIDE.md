# 🚀 Arduino Controller - دليل بسيط

## 📁 الملفات الأساسية فقط
```
📂 المشروع/
├── 🔧 src/arduino_controller.cpp    # كود Arduino
├── 🖥️ arduino_gui.py               # الواجهة العصرية
├── ▶️ run_arduino_gui.py           # مشغل الواجهة
├── 📋 requirements.txt             # المتطلبات (pyserial فقط)
├── 🪟 run.bat                      # تشغيل Windows
└── 🐧 run.sh                       # تشغيل Linux/Mac
```

## ⚡ تشغيل سريع (3 خطوات)

### 1️⃣ تثبيت المتطلبات
```bash
pip install pyserial
```

### 2️⃣ رفع كود Arduino
```
افتح Arduino IDE
اختر src/arduino_controller.cpp
ارفع للـ Arduino Uno R4 WiFi
```

### 3️⃣ تشغيل الواجهة
```bash
# Windows
run.bat

# أو
python run_arduino_gui.py

# Linux/Mac
chmod +x run.sh && ./run.sh

# أو
python3 run_arduino_gui.py
```

## 🎯 الاستخدام
1. **اختر المنفذ** من القائمة
2. **اضغط اتصال** 🔗
3. **استمتع بالتحكم** 🎮

## ✨ المميزات
- **🎨 تصميم عصري**: ثيمات داكن/فاتح
- **📱 متجاوب**: يعمل على جميع الشاشات
- **🎛️ تحكم كامل**: PWM, نبضات, محرك, مرحلات
- **📊 سجل ملون**: متابعة العمليات

## 🛠️ حل المشاكل
```bash
# مشكلة: pyserial مفقود
pip install pyserial

# مشكلة: المنفذ مشغول
# أغلق أي برنامج آخر يستخدم Arduino

# مشكلة: لا توجد استجابة
# تأكد من رفع الكود الصحيح
```

---
**🎉 بساطة وأناقة في التحكم!**

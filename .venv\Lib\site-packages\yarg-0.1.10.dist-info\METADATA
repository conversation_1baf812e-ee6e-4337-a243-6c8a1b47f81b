Metadata-Version: 2.1
Name: yarg
Version: 0.1.10
Summary: A semi hard Cornish cheese, also queries PyPI (PyPI client)
Home-page: https://yarg.readthedocs.org/
Author: <PERSON>ra
Author-email: <EMAIL>
Maintainer: <PERSON>ra
Maintainer-email: <EMAIL>
License: MIT
Keywords: pypi,client,packages
Platform: linux
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Natural Language :: English
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: System :: Archiving :: Packaging
Requires: requests
Provides: yarg
License-File: LICENSE
License-File: LICENSE-REQUESTS
Requires-Dist: requests

yarg(1) -- A semi hard Cornish cheese, also queries PyPI
========================================================

.. image:: https://img.shields.io/travis/kura/yarg.svg?style=flat

.. image:: https://img.shields.io/coveralls/kura/yarg.svg?style=flat

Yarg is a PyPI client.

.. code-block:: python

    >>> import yarg
    >>> package = yarg.get("yarg")
    >>> package.name
    u'yarg'
    >>> package.author
    Author(name=u'Kura', email=u'<EMAIL>')

Full documentation is at <https://yarg.readthedocs.org>.

Yarg is released under the `MIT license
<https://github.com/kura/yarg/blob/master/LICENSE>`_. The `source code is on
GitHub <https://github.com/kura/yarg>`_ and `issues are also tracked on
GitHub <https://github.com/kura/yarg/issues>`_.


Release History
===============

0.1.10 (2024-08-09)
- Python 3.12 support

0.1.9 (2014-08-11)
------------------

Splatting bugs
~~~~~~~~~~~~~~

- Added `decode` call on the response object for Python 3 in
  `yarg.newest_packages` and `yarg.latest_updated_packages`.

0.1.8 (2014-08-10)
------------------

Splatting bugs
~~~~~~~~~~~~~~

- Integration issue with Python 3, requests, yarg and JSON. Attempt to decode
  requests response if decode attribute exists.

0.1.6 & 0.1.7 (2014-08-10)
--------------------------

Splatting bugs
~~~~~~~~~~~~~~

- Bug in setup.py causing installs to fail for sdist (source) releases.

0.1.5 (2014-08-10)
------------------

API changes
~~~~~~~~~~~

- Changed sort order of `yarg.package.Package.release_ids` to sort
  based on the upload time of the release ID.

Splatting bugs
~~~~~~~~~~~~~~

- `yarg.package.Package.latest_release_id` will now return the latest
  release ID from the PyPI info source, rather than the final list item in
  `yarg.package.Package.release_ids`.

  Addtionally `yarg.package.Package.latest_release` will do the same as
  it gets the latest release information from
  `yarg.package.Package.latest_release_id`.

0.1.4 (2014-08-09)
------------------

API changes
~~~~~~~~~~~

- New method `yarg.newest_packages` for querying new packages
  from the PyPI RSS feed.
- New method `yarg.latest_updated_packages` for querying
  the latest updated packages from the PyPI RSS feed.

Other
~~~~~

- Additional test coverage
- Additional documentation coverage

0.1.2 (2014-08-08)
------------------

Bug fixes
~~~~~~~~~

- `yarg.get` will now raise an Exception for errors **including**
  300 and above. Previously only raised for above 300.
- Fix an issue on Python 3.X and PyPy3 where
  `yarg.exceptions.HTTPError` was using a method that was
  removed in Python 3.
- Added dictionary key lookups for `home_page`, `bugtrack_url`
  and `docs_url`. Caused `KeyError` exceptions if they were not
  returned by PyPI.

Other
~~~~~

- More test coverage.

0.1.1 (2014-08-08)
------------------

API changes
~~~~~~~~~~~

- New `yarg.package.Package` property `has_wheel`.
- New `yarg.package.Package` property `has_egg`.
- New `yarg.package.Package` property `has_source`.
- New `yarg.package.Package` property `python_versions`.
- New `yarg.package.Package` property `python_implementations`.
- Added `yarg.exceptions.HTTPError` to `yarg.__init__`
  for easier access.
- Added `yarg.json2package` to `yarg.__init__` to expose it for
  use.

0.1.0 (2014-08-08)
------------------

- Initial release

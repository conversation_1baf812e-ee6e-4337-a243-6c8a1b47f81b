#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arduino Serial Controller GUI - Modern & Responsive
واجهة تحكم Arduino عبر السيريال - عصرية ومتجاوبة
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import serial
import serial.tools.list_ports
import threading
import time
import json
import os

class ModernArduinoController:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_styles()

        # Serial connection
        self.serial_connection = None
        self.is_connected = False
        self.auto_refresh = False

        # Data storage
        self.pwm_values = [0, 0, 0]
        self.stepper_data = {"angle": 0.0, "speed": 12, "mode": "IDLE"}
        self.relay_data = {
            "right": {"active": False, "timer": 5, "remaining": 0},
            "left": {"active": False, "timer": 5, "remaining": 0}
        }
        self.shoot_rate = 1
        self.continuous_shooting = False

        # Theme settings
        self.current_theme = "dark"  # dark or light
        self.themes = self.setup_themes()

        # Settings file
        self.settings_file = "arduino_gui_settings.json"
        self.load_settings()

        # Apply theme first before creating widgets
        self.apply_theme()

        self.create_widgets()
        self.refresh_ports()

        # Auto refresh timer
        self.auto_refresh_timer()

        # Responsive design
        self.setup_responsive_design()

    def setup_window(self):
        """Setup main window with modern design"""
        self.root.title("🚀 Arduino Controller Pro - تحكم Arduino المتقدم")

        # Get screen dimensions for responsive design
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Calculate window size based on screen size
        if screen_width <= 800:  # Mobile/small tablet
            width, height = int(screen_width * 0.95), int(screen_height * 0.9)
        elif screen_width <= 1366:  # Tablet/small laptop
            width, height = int(screen_width * 0.85), int(screen_height * 0.8)
        else:  # Desktop/large laptop
            width, height = min(1400, int(screen_width * 0.75)), min(900, int(screen_height * 0.8))

        # Center window on screen
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        self.root.geometry(f"{width}x{height}+{x}+{y}")
        self.root.minsize(800, 600)  # Minimum size

        # Modern window styling
        self.root.configure(bg='#1e1e1e')

        # Make window resizable and responsive
        self.root.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)

    def setup_styles(self):
        """Setup modern ttk styles"""
        self.style = ttk.Style()

        # Configure modern styles
        self.style.theme_use('clam')

    def setup_themes(self):
        """Setup dark and light themes"""
        return {
            "dark": {
                "bg": "#1e1e1e",
                "fg": "#ffffff",
                "select_bg": "#0d7377",
                "select_fg": "#ffffff",
                "entry_bg": "#2d2d2d",
                "entry_fg": "#ffffff",
                "button_bg": "#0d7377",
                "button_fg": "#ffffff",
                "button_hover": "#14a085",
                "frame_bg": "#2d2d2d",
                "accent": "#00d4aa",
                "warning": "#ff6b6b",
                "success": "#51cf66",
                "info": "#339af0"
            },
            "light": {
                "bg": "#f8f9fa",
                "fg": "#212529",
                "select_bg": "#0d7377",
                "select_fg": "#ffffff",
                "entry_bg": "#ffffff",
                "entry_fg": "#212529",
                "button_bg": "#0d7377",
                "button_fg": "#ffffff",
                "button_hover": "#14a085",
                "frame_bg": "#ffffff",
                "accent": "#0d7377",
                "warning": "#e03131",
                "success": "#2b8a3e",
                "info": "#1971c2"
            }
        }

    def apply_theme(self):
        """Apply current theme to all widgets"""
        theme = self.themes[self.current_theme]

        # Configure root window
        self.root.configure(bg=theme["bg"])

        # Configure ttk styles with error handling
        try:
            self.style.configure('Modern.TFrame',
                               background=theme["frame_bg"],
                               relief='flat',
                               borderwidth=1)

            self.style.configure('Modern.TLabelFrame',
                               background=theme["frame_bg"],
                               foreground=theme["fg"],
                               relief='flat',
                               borderwidth=2,
                               labelmargins=(10, 5, 10, 5))

            self.style.configure('Modern.TLabel',
                               background=theme["frame_bg"],
                               foreground=theme["fg"],
                               font=('Segoe UI', 10))

            self.style.configure('Title.TLabel',
                               background=theme["bg"],
                               foreground=theme["accent"],
                               font=('Segoe UI', 16, 'bold'))

            self.style.configure('Modern.TButton',
                               background=theme["button_bg"],
                               foreground=theme["button_fg"],
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 9, 'bold'),
                               padding=(15, 8))

            self.style.map('Modern.TButton',
                          background=[('active', theme["button_hover"]),
                                    ('pressed', theme["select_bg"])])

            self.style.configure('Accent.TButton',
                               background=theme["accent"],
                               foreground='#ffffff',
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 10, 'bold'),
                               padding=(20, 10))

            self.style.map('Accent.TButton',
                          background=[('active', theme["button_hover"]),
                                    ('pressed', theme["select_bg"])])

            self.style.configure('Success.TButton',
                               background=theme["success"],
                               foreground='#ffffff',
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 9, 'bold'),
                               padding=(15, 8))

            self.style.configure('Warning.TButton',
                               background=theme["warning"],
                               foreground='#ffffff',
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 9, 'bold'),
                               padding=(15, 8))

            self.style.configure('Modern.TCombobox',
                               fieldbackground=theme["entry_bg"],
                               background=theme["entry_bg"],
                               foreground=theme["entry_fg"],
                               borderwidth=1,
                               insertcolor=theme["fg"])

            self.style.configure('Modern.TEntry',
                               fieldbackground=theme["entry_bg"],
                               foreground=theme["entry_fg"],
                               borderwidth=1,
                               insertcolor=theme["fg"])

            self.style.configure('Modern.TNotebook',
                               background=theme["bg"],
                               borderwidth=0,
                               tabmargins=(2, 5, 2, 0))

            self.style.configure('Modern.TNotebook.Tab',
                               background=theme["frame_bg"],
                               foreground=theme["fg"],
                               padding=(20, 12),
                               borderwidth=0,
                               font=('Segoe UI', 10, 'bold'))

            self.style.map('Modern.TNotebook.Tab',
                          background=[('selected', theme["accent"]),
                                    ('active', theme["button_hover"])],
                          foreground=[('selected', '#ffffff'),
                                    ('active', '#ffffff')])
        except Exception as e:
            print(f"Warning: Could not apply some styles: {e}")
            # Fall back to basic styling if custom styles fail

    def setup_responsive_design(self):
        """Setup responsive design handlers"""
        self.root.bind('<Configure>', self.on_window_resize)

    def on_window_resize(self, event):
        """Handle window resize for responsive design"""
        if event.widget == self.root:
            width = event.width
            height = event.height

            # Adjust layout based on window size
            if hasattr(self, 'main_container'):
                if width < 900:  # Compact mode
                    self.switch_to_compact_mode()
                else:  # Normal mode
                    self.switch_to_normal_mode()

    def switch_to_compact_mode(self):
        """Switch to compact layout for small screens"""
        # This will be implemented in the widget creation
        pass

    def switch_to_normal_mode(self):
        """Switch to normal layout for larger screens"""
        # This will be implemented in the widget creation
        pass
        
    def create_widgets(self):
        """Create modern responsive widgets"""
        # Main container with modern styling
        self.main_container = ttk.Frame(self.root, padding="20")
        self.main_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights for responsiveness
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_container.columnconfigure(0, weight=1)
        self.main_container.rowconfigure(2, weight=1)

        # Create header with title and theme toggle
        self.create_header()

        # Connection frame with modern design
        self.create_modern_connection_frame()

        # Control tabs with modern styling
        self.create_modern_control_tabs()

        # Status and log frame with modern design
        self.create_modern_status_frame()

    def create_header(self):
        """Create modern header with title and controls"""
        header_frame = ttk.Frame(self.main_container)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        header_frame.columnconfigure(1, weight=1)

        # App icon and title
        title_frame = ttk.Frame(header_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W))

        title_label = ttk.Label(title_frame, text="🚀 Arduino Controller Pro",
                               font=('Segoe UI', 16, 'bold'))
        title_label.pack(side=tk.LEFT)

        subtitle_label = ttk.Label(title_frame, text="تحكم Arduino المتقدم",
                                  font=('Segoe UI', 10, 'italic'))
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0))

        # Control buttons frame
        controls_frame = ttk.Frame(header_frame)
        controls_frame.grid(row=0, column=2, sticky=(tk.E))

        # Theme toggle button
        self.theme_btn = ttk.Button(controls_frame, text="🌙 Dark",
                                   command=self.toggle_theme)
        self.theme_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # Minimize/Maximize buttons
        self.fullscreen_btn = ttk.Button(controls_frame, text="⛶",
                                        command=self.toggle_fullscreen)
        self.fullscreen_btn.pack(side=tk.RIGHT, padx=(5, 0))

    def toggle_theme(self):
        """Toggle between dark and light themes"""
        if self.current_theme == "dark":
            self.current_theme = "light"
            self.theme_btn.config(text="☀️ Light")
        else:
            self.current_theme = "dark"
            self.theme_btn.config(text="🌙 Dark")

        self.apply_theme()

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)

        if current_state:
            self.fullscreen_btn.config(text="⛶")
        else:
            self.fullscreen_btn.config(text="🗗")
        
    def create_modern_connection_frame(self):
        """Create modern connection frame with responsive design"""
        conn_frame = ttk.LabelFrame(self.main_container, text="🔌 اتصال السيريال - Serial Connection",
                                   padding="20")
        conn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        conn_frame.columnconfigure(1, weight=1)
        conn_frame.columnconfigure(3, weight=1)

        # Connection status indicator (prominent)
        status_frame = ttk.Frame(conn_frame)
        status_frame.grid(row=0, column=0, columnspan=6, sticky=(tk.W, tk.E), pady=(0, 15))

        self.connection_indicator = tk.Canvas(status_frame, width=20, height=20,
                                            highlightthickness=0, bg=self.themes[self.current_theme]["frame_bg"])
        self.connection_indicator.pack(side=tk.LEFT, padx=(0, 10))

        self.status_label = ttk.Label(status_frame, text="⚫ غير متصل - Disconnected",
                                     font=('Segoe UI', 11, 'bold'))
        self.status_label.pack(side=tk.LEFT)

        # Connection controls in responsive grid
        controls_frame = ttk.Frame(conn_frame)
        controls_frame.grid(row=1, column=0, columnspan=6, sticky=(tk.W, tk.E))
        controls_frame.columnconfigure(1, weight=1)
        controls_frame.columnconfigure(3, weight=1)

        # Port selection with modern styling
        ttk.Label(controls_frame, text="📍 المنفذ",
                 font=('Segoe UI', 10, 'bold')).grid(row=0, column=0, padx=(0, 10), sticky=tk.W)

        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(controls_frame, textvariable=self.port_var,
                                      font=('Segoe UI', 10), width=20)
        self.port_combo.grid(row=0, column=1, padx=(0, 20), sticky=(tk.W, tk.E))

        # Baud rate selection
        ttk.Label(controls_frame, text="⚡ السرعة",
                 font=('Segoe UI', 10, 'bold')).grid(row=0, column=2, padx=(0, 10), sticky=tk.W)

        self.baud_var = tk.StringVar(value="115200")
        baud_combo = ttk.Combobox(controls_frame, textvariable=self.baud_var,
                                 font=('Segoe UI', 10), width=15,
                                 values=["9600", "57600", "115200"], state="readonly")
        baud_combo.grid(row=0, column=3, padx=(0, 20), sticky=(tk.W, tk.E))

        # Action buttons with modern styling
        buttons_frame = ttk.Frame(controls_frame)
        buttons_frame.grid(row=0, column=4, sticky=tk.E)

        self.refresh_btn = ttk.Button(buttons_frame, text="🔄 تحديث",
                                     command=self.refresh_ports)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.connect_btn = ttk.Button(buttons_frame, text="🔗 اتصال",
                                     command=self.toggle_connection)
        self.connect_btn.pack(side=tk.LEFT)

        # Update connection indicator
        self.update_connection_indicator()

    def update_connection_indicator(self):
        """Update visual connection indicator"""
        self.connection_indicator.delete("all")
        if self.is_connected:
            # Green circle for connected
            self.connection_indicator.create_oval(2, 2, 18, 18,
                                                fill=self.themes[self.current_theme]["success"],
                                                outline="")
            self.status_label.config(text="🟢 متصل - Connected")
        else:
            # Red circle for disconnected
            self.connection_indicator.create_oval(2, 2, 18, 18,
                                                fill=self.themes[self.current_theme]["warning"],
                                                outline="")
            self.status_label.config(text="🔴 غير متصل - Disconnected")

    def create_modern_control_tabs(self):
        """Create modern control tabs with responsive design"""
        # Modern notebook with custom styling
        self.notebook = ttk.Notebook(self.main_container)
        self.notebook.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))

        # Create tabs with modern design
        self.create_modern_pwm_tab()
        self.create_modern_shooting_tab()
        self.create_modern_stepper_tab()
        self.create_modern_relay_tab()
        self.create_modern_settings_tab()
        
    def create_modern_pwm_tab(self):
        """Create modern PWM control tab with visual enhancements"""
        pwm_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(pwm_frame, text="🎨 تحكم PWM")

        # Configure responsive grid
        pwm_frame.columnconfigure(0, weight=1)

        # PWM channels with modern design
        self.pwm_vars = []
        self.pwm_scales = []
        self.pwm_labels = []
        self.pwm_canvases = []

        channels = [
            ("🔴 الأحمر - Red (D9)", "#ff4757", "#ff3838"),
            ("🔵 الأزرق - Blue (D6)", "#3742fa", "#2f3542"),
            ("🟢 الأخضر - Green (D5)", "#2ed573", "#1e90ff")
        ]

        for i, (name, color_main, color_accent) in enumerate(channels):
            # Modern card-style frame for each channel
            card_frame = ttk.LabelFrame(pwm_frame, text=name, padding="20")
            card_frame.grid(row=i, column=0, sticky=(tk.W, tk.E), padx=0, pady=10)
            card_frame.columnconfigure(1, weight=1)

            # Visual indicator (color preview)
            indicator_frame = ttk.Frame(card_frame)
            indicator_frame.grid(row=0, column=0, padx=(0, 15), sticky=tk.W)

            color_canvas = tk.Canvas(indicator_frame, width=60, height=60,
                                   highlightthickness=2, highlightbackground=color_main)
            color_canvas.pack()
            color_canvas.create_rectangle(2, 2, 58, 58, fill=color_main, outline="")
            self.pwm_canvases.append(color_canvas)

            # Control section
            control_frame = ttk.Frame(card_frame)
            control_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))
            control_frame.columnconfigure(0, weight=1)

            # Value variable
            var = tk.IntVar(value=self.pwm_values[i])
            self.pwm_vars.append(var)

            # Modern slider with custom styling
            slider_frame = ttk.Frame(control_frame)
            slider_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
            slider_frame.columnconfigure(0, weight=1)

            scale = tk.Scale(slider_frame, from_=0, to=255, orient=tk.HORIZONTAL,
                           variable=var, command=lambda val, ch=i: self.on_pwm_change(ch, val),
                           bg=self.themes[self.current_theme]["frame_bg"],
                           fg=self.themes[self.current_theme]["fg"],
                           activebackground=color_main,
                           highlightthickness=0,
                           troughcolor=self.themes[self.current_theme]["entry_bg"],
                           font=('Segoe UI', 9, 'bold'),
                           length=300, width=20)
            scale.grid(row=0, column=0, sticky=(tk.W, tk.E))
            self.pwm_scales.append(scale)

            # Value display section
            value_frame = ttk.Frame(control_frame)
            value_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

            # Digital value display
            value_label = ttk.Label(value_frame, text="0 (0.00V)",
                                   font=('Segoe UI', 12, 'bold'))
            value_label.pack(side=tk.LEFT)
            self.pwm_labels.append(value_label)

            # Quick preset buttons with modern styling
            presets_frame = ttk.Frame(card_frame)
            presets_frame.grid(row=0, column=2, sticky=tk.E)

            preset_values = [("OFF", 0), ("25%", 64), ("50%", 128), ("75%", 192), ("MAX", 255)]

            for j, (label, value) in enumerate(preset_values):
                btn = ttk.Button(presets_frame, text=label,
                               command=lambda ch=i, val=value: self.set_pwm_value(ch, val),
                               width=6)
                btn.grid(row=j, column=0, pady=2, sticky=(tk.W, tk.E))

        # Update PWM display
        self.update_pwm_display()

    def create_modern_shooting_tab(self):
        """Create modern shooting control tab"""
        shoot_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(shoot_frame, text="⚡ تحكم النبضات")

        shoot_frame.columnconfigure(0, weight=1)

        # Single shot section with modern design
        single_card = ttk.LabelFrame(shoot_frame, text="⚡ نبضة واحدة - Single Pulse",
                                    padding="25")
        single_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # Large pulse button with visual feedback
        pulse_frame = ttk.Frame(single_card)
        pulse_frame.pack(expand=True)

        self.pulse_btn = ttk.Button(pulse_frame, text="🔥 إرسال نبضة\nSend Pulse",
                                   command=self.single_shoot)
        self.pulse_btn.pack(pady=10)

        # Pulse indicator
        self.pulse_indicator = tk.Canvas(pulse_frame, width=30, height=30,
                                       highlightthickness=0, bg=self.themes[self.current_theme]["frame_bg"])
        self.pulse_indicator.pack(pady=(10, 0))

        # Continuous shooting section
        cont_card = ttk.LabelFrame(shoot_frame, text="🔄 نبضات مستمرة - Continuous Pulses",
                                  padding="25")
        cont_card.grid(row=1, column=0, sticky=(tk.W, tk.E))
        cont_card.columnconfigure(0, weight=1)

        # Rate control with modern slider
        rate_section = ttk.Frame(cont_card)
        rate_section.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        rate_section.columnconfigure(1, weight=1)

        ttk.Label(rate_section, text="📊 معدل النبضات",
                 font=('Segoe UI', 11, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 15))

        self.rate_var = tk.IntVar(value=self.shoot_rate)
        rate_scale = tk.Scale(rate_section, from_=0, to=10, orient=tk.HORIZONTAL,
                             variable=self.rate_var, command=self.on_rate_change,
                             bg=self.themes[self.current_theme]["frame_bg"],
                             fg=self.themes[self.current_theme]["fg"],
                             activebackground=self.themes[self.current_theme]["accent"],
                             highlightthickness=0,
                             troughcolor=self.themes[self.current_theme]["entry_bg"],
                             font=('Segoe UI', 10, 'bold'),
                             length=250, width=25)
        rate_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))

        self.rate_label = ttk.Label(rate_section, text=f"{self.shoot_rate} Hz",
                                   font=('Segoe UI', 12, 'bold'))
        self.rate_label.grid(row=0, column=2, sticky=tk.E)

        # Control buttons with modern styling
        controls_section = ttk.Frame(cont_card)
        controls_section.grid(row=1, column=0, pady=(0, 15))

        self.start_shoot_btn = ttk.Button(controls_section, text="▶️ بدء النبضات",
                                         command=self.start_continuous_shooting)
        self.start_shoot_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_shoot_btn = ttk.Button(controls_section, text="⏹️ إيقاف النبضات",
                                        command=self.stop_continuous_shooting)
        self.stop_shoot_btn.pack(side=tk.LEFT)

        # Status display with visual indicator
        status_section = ttk.Frame(cont_card)
        status_section.grid(row=2, column=0, sticky=(tk.W, tk.E))

        self.shoot_status_indicator = tk.Canvas(status_section, width=20, height=20,
                                              highlightthickness=0, bg=self.themes[self.current_theme]["frame_bg"])
        self.shoot_status_indicator.pack(side=tk.LEFT, padx=(0, 10))

        self.shoot_status_label = ttk.Label(status_section, text="⏸️ الحالة: متوقف",
                                           font=('Segoe UI', 11, 'bold'))
        self.shoot_status_label.pack(side=tk.LEFT)

        # Update shooting status indicator
        self.update_shooting_indicator()

    def update_shooting_indicator(self):
        """Create modern stepper motor control tab"""
        stepper_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(stepper_frame, text="🔄 المحرك المتدرج")

        stepper_frame.columnconfigure(0, weight=1)
        stepper_frame.columnconfigure(1, weight=1)

        # Status display
        status_card = ttk.LabelFrame(stepper_frame, text="📊 حالة المحرك - Motor Status", padding="20")
        status_card.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        self.stepper_status_label = ttk.Label(status_card, text="الزاوية: 0.0° | السرعة: 12 RPM | الوضع: IDLE",
                                             font=('Segoe UI', 11, 'bold'))
        self.stepper_status_label.pack()

        # Angle control
        angle_card = ttk.LabelFrame(stepper_frame, text="🎯 التحكم بالزاوية - Angle Control", padding="20")
        angle_card.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10), pady=(0, 10))

        # Angle input
        angle_input_frame = ttk.Frame(angle_card)
        angle_input_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(angle_input_frame, text="الزاوية المطلوبة (0-359):", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.angle_var = tk.IntVar(value=0)
        angle_entry = ttk.Entry(angle_input_frame, textvariable=self.angle_var, width=15, font=('Segoe UI', 12))
        angle_entry.pack(pady=(5, 10))

        ttk.Button(angle_input_frame, text="🎯 انتقال للزاوية", command=self.goto_angle).pack()

        # Speed control
        speed_frame = ttk.Frame(angle_card)
        speed_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(speed_frame, text="السرعة (1-20 RPM):", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.speed_var = tk.IntVar(value=self.stepper_data["speed"])
        speed_scale = tk.Scale(speed_frame, from_=1, to=20, orient=tk.HORIZONTAL,
                              variable=self.speed_var, command=self.on_speed_change,
                              font=('Segoe UI', 10), length=200)
        speed_scale.pack(pady=(5, 0))

        self.speed_label = ttk.Label(speed_frame, text=f"{self.stepper_data['speed']} RPM",
                                    font=('Segoe UI', 11, 'bold'))
        self.speed_label.pack()

        # Movement control
        move_card = ttk.LabelFrame(stepper_frame, text="🎮 التحكم بالحركة - Movement Control", padding="20")
        move_card.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0), pady=(0, 10))

        ttk.Button(move_card, text="↻ دوران مع عقارب الساعة", command=self.stepper_cw).pack(fill=tk.X, pady=5)
        ttk.Button(move_card, text="↺ دوران عكس عقارب الساعة", command=self.stepper_ccw).pack(fill=tk.X, pady=5)
        ttk.Button(move_card, text="⏹️ إيقاف الحركة", command=self.stepper_stop).pack(fill=tk.X, pady=5)
        ttk.Button(move_card, text="🔄 إعادة تعيين الموضع", command=self.stepper_reset).pack(fill=tk.X, pady=5)

    def create_modern_relay_tab(self):
        """Create modern relay control tab"""
        relay_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(relay_frame, text="🔌 تحكم المرحلات")

        relay_frame.columnconfigure(0, weight=1)
        relay_frame.columnconfigure(1, weight=1)

        # Right relay
        self.create_modern_relay_control(relay_frame, "RIGHT", "🔌 المرحل الأيمن (D10)", 0, 0)

        # Left relay
        self.create_modern_relay_control(relay_frame, "LEFT", "🔌 المرحل الأيسر (D11)", 0, 1)

    def create_modern_relay_control(self, parent, relay_id, title, row, col):
        """Create modern relay control card"""
        card = ttk.LabelFrame(parent, text=title, padding="20")
        card.grid(row=row, column=col, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)

        # Timer setting
        timer_frame = ttk.Frame(card)
        timer_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(timer_frame, text="المؤقت (ثانية، 0 للتشغيل اليدوي):",
                 font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        timer_var = tk.IntVar(value=self.relay_data[relay_id.lower()]["timer"])
        setattr(self, f"timer_{relay_id.lower()}_var", timer_var)

        timer_entry = ttk.Entry(timer_frame, textvariable=timer_var, width=15, font=('Segoe UI', 12))
        timer_entry.pack(pady=(5, 0))

        # Control buttons
        btn_frame = ttk.Frame(card)
        btn_frame.pack(fill=tk.X, pady=(0, 15))

        on_btn = ttk.Button(btn_frame, text=f"🟢 تشغيل",
                           command=lambda: self.relay_on(relay_id))
        on_btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)

        off_btn = ttk.Button(btn_frame, text=f"🔴 إيقاف",
                            command=lambda: self.relay_off(relay_id))
        off_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Status display
        status_label = ttk.Label(card, text="الحالة: متوقف", font=('Segoe UI', 11, 'bold'))
        status_label.pack()
        setattr(self, f"relay_{relay_id.lower()}_status", status_label)

    def create_modern_settings_tab(self):
        """Create modern settings tab"""
        settings_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(settings_frame, text="⚙️ الإعدادات")

        settings_frame.columnconfigure(0, weight=1)

        # Auto refresh
        auto_card = ttk.LabelFrame(settings_frame, text="🔄 التحديث التلقائي", padding="20")
        auto_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        self.auto_refresh_var = tk.BooleanVar(value=self.auto_refresh)
        ttk.Checkbutton(auto_card, text="تحديث تلقائي للحالة كل ثانيتين",
                       variable=self.auto_refresh_var, command=self.toggle_auto_refresh).pack(anchor=tk.W)

        ttk.Button(auto_card, text="🔄 تحديث الحالة يدوياً",
                  command=self.manual_refresh).pack(pady=(15, 0))

        # Save/Load settings
        save_card = ttk.LabelFrame(settings_frame, text="💾 حفظ الإعدادات", padding="20")
        save_card.grid(row=1, column=0, sticky=(tk.W, tk.E))

        ttk.Button(save_card, text="💾 حفظ الإعدادات في Arduino",
                  command=self.save_arduino_settings).pack(fill=tk.X, pady=5)
        ttk.Button(save_card, text="🔄 إعادة تعيين Arduino للإعدادات الافتراضية",
                  command=self.reset_arduino_settings).pack(fill=tk.X, pady=5)
        """Update shooting status visual indicator"""
        self.shoot_status_indicator.delete("all")
        if self.continuous_shooting:
            # Animated green circle for active shooting
            self.shoot_status_indicator.create_oval(2, 2, 18, 18,
                                                   fill=self.themes[self.current_theme]["success"],
                                                   outline="")
            self.shoot_status_label.config(text=f"🟢 نشط: {self.shoot_rate} Hz")
        else:
            # Gray circle for stopped
            self.shoot_status_indicator.create_oval(2, 2, 18, 18,
                                                   fill=self.themes[self.current_theme]["entry_bg"],
                                                   outline="")
            self.shoot_status_label.config(text="⏸️ الحالة: متوقف")

    def flash_pulse_indicator(self):
        """Flash pulse indicator for visual feedback"""
        self.pulse_indicator.delete("all")
        self.pulse_indicator.create_oval(2, 2, 28, 28,
                                       fill=self.themes[self.current_theme]["accent"],
                                       outline="")
        # Clear after 200ms
        self.root.after(200, lambda: self.pulse_indicator.delete("all"))

    def create_modern_stepper_tab(self):
        stepper_frame = ttk.Frame(self.notebook)
        self.notebook.add(stepper_frame, text="المحرك المتدرج")
        
        # Status display
        status_frame = ttk.LabelFrame(stepper_frame, text="حالة المحرك - Motor Status", padding="10")
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        self.stepper_status_label = ttk.Label(status_frame, text="الزاوية: 0.0° | السرعة: 12 RPM | الوضع: IDLE")
        self.stepper_status_label.pack()
        
        # Angle control
        angle_frame = ttk.LabelFrame(stepper_frame, text="التحكم بالزاوية - Angle Control", padding="10")
        angle_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        angle_input_frame = ttk.Frame(angle_frame)
        angle_input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(angle_input_frame, text="الزاوية المطلوبة (0-359):").pack(side=tk.LEFT)
        self.angle_var = tk.IntVar(value=0)
        angle_entry = ttk.Entry(angle_input_frame, textvariable=self.angle_var, width=10)
        angle_entry.pack(side=tk.LEFT, padx=(10, 10))
        
        ttk.Button(angle_input_frame, text="انتقال للزاوية", 
                  command=self.goto_angle).pack(side=tk.LEFT)
        
        # Speed control
        speed_frame = ttk.Frame(angle_frame)
        speed_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(speed_frame, text="السرعة (1-20 RPM):").pack(side=tk.LEFT)
        self.speed_var = tk.IntVar(value=self.stepper_data["speed"])
        speed_scale = tk.Scale(speed_frame, from_=1, to=20, orient=tk.HORIZONTAL,
                              variable=self.speed_var, command=self.on_speed_change)
        speed_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 10))
        
        self.speed_label = ttk.Label(speed_frame, text=f"{self.stepper_data['speed']} RPM")
        self.speed_label.pack(side=tk.LEFT)
        
        # Movement control
        move_frame = ttk.LabelFrame(stepper_frame, text="التحكم بالحركة - Movement Control", padding="10")
        move_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        ttk.Button(move_frame, text="دوران مع عقارب الساعة", 
                  command=self.stepper_cw).pack(fill=tk.X, pady=2)
        ttk.Button(move_frame, text="دوران عكس عقارب الساعة", 
                  command=self.stepper_ccw).pack(fill=tk.X, pady=2)
        ttk.Button(move_frame, text="إيقاف الحركة", 
                  command=self.stepper_stop).pack(fill=tk.X, pady=2)
        ttk.Button(move_frame, text="إعادة تعيين الموضع", 
                  command=self.stepper_reset).pack(fill=tk.X, pady=2)
        
    def create_relay_tab(self):
        relay_frame = ttk.Frame(self.notebook)
        self.notebook.add(relay_frame, text="تحكم المرحلات")
        
        # Right relay
        self.create_relay_control(relay_frame, "RIGHT", "المرحل الأيمن (D10)", 0)
        
        # Left relay  
        self.create_relay_control(relay_frame, "LEFT", "المرحل الأيسر (D11)", 1)
        
    def create_relay_control(self, parent, relay_id, title, row):
        frame = ttk.LabelFrame(parent, text=title, padding="10")
        frame.grid(row=row, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        frame.columnconfigure(1, weight=1)
        
        # Timer setting
        timer_frame = ttk.Frame(frame)
        timer_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(timer_frame, text="المؤقت (ثانية، 0 للتشغيل اليدوي):").pack(side=tk.LEFT)
        
        timer_var = tk.IntVar(value=self.relay_data[relay_id.lower()]["timer"])
        setattr(self, f"timer_{relay_id.lower()}_var", timer_var)
        
        timer_entry = ttk.Entry(timer_frame, textvariable=timer_var, width=10)
        timer_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # Control buttons
        btn_frame = ttk.Frame(frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        on_btn = ttk.Button(btn_frame, text=f"تشغيل {title.split()[0]}", 
                           command=lambda: self.relay_on(relay_id))
        on_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        off_btn = ttk.Button(btn_frame, text=f"إيقاف {title.split()[0]}", 
                            command=lambda: self.relay_off(relay_id))
        off_btn.pack(side=tk.LEFT)
        
        # Status display
        status_label = ttk.Label(frame, text="الحالة: متوقف")
        status_label.grid(row=2, column=0, columnspan=2, pady=(10, 0))
        setattr(self, f"relay_{relay_id.lower()}_status", status_label)
        
    def create_settings_tab(self):
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="الإعدادات")
        
        # Auto refresh
        auto_frame = ttk.LabelFrame(settings_frame, text="التحديث التلقائي", padding="10")
        auto_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        self.auto_refresh_var = tk.BooleanVar(value=self.auto_refresh)
        ttk.Checkbutton(auto_frame, text="تحديث تلقائي للحالة كل ثانيتين", 
                       variable=self.auto_refresh_var,
                       command=self.toggle_auto_refresh).pack()
        
        # Manual refresh
        ttk.Button(auto_frame, text="تحديث الحالة يدوياً", 
                  command=self.manual_refresh).pack(pady=(10, 0))
        
        # Save/Load settings
        save_frame = ttk.LabelFrame(settings_frame, text="حفظ الإعدادات", padding="10")
        save_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        ttk.Button(save_frame, text="حفظ الإعدادات في Arduino", 
                  command=self.save_arduino_settings).pack(pady=2)
        ttk.Button(save_frame, text="إعادة تعيين Arduino للإعدادات الافتراضية", 
                  command=self.reset_arduino_settings).pack(pady=2)
        
    def create_modern_status_frame(self):
        """Create modern status and log frame"""
        status_frame = ttk.LabelFrame(self.main_container, text="📊 سجل الأحداث - Event Log",
                                     padding="15")
        status_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(0, weight=1)

        # Log controls frame
        controls_frame = ttk.Frame(status_frame)
        controls_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        controls_frame.columnconfigure(0, weight=1)

        # Log title and controls
        title_frame = ttk.Frame(controls_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(title_frame, text="📝 سجل الاتصالات",
                 font=('Segoe UI', 11, 'bold')).pack(side=tk.LEFT)

        # Log control buttons
        log_controls = ttk.Frame(title_frame)
        log_controls.pack(side=tk.RIGHT)

        ttk.Button(log_controls, text="🗑️ مسح", command=self.clear_log).pack(side=tk.RIGHT, padx=(5, 0))

        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_controls, text="تمرير تلقائي", variable=self.auto_scroll_var).pack(side=tk.RIGHT, padx=(5, 0))

        # Modern log text area with custom styling
        log_container = ttk.Frame(status_frame)
        log_container.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_container.columnconfigure(0, weight=1)
        log_container.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(
            log_container,
            height=8,
            width=80,
            bg=self.themes[self.current_theme]["entry_bg"],
            fg=self.themes[self.current_theme]["entry_fg"],
            insertbackground=self.themes[self.current_theme]["fg"],
            selectbackground=self.themes[self.current_theme]["select_bg"],
            selectforeground=self.themes[self.current_theme]["select_fg"],
            font=('Consolas', 9),
            wrap=tk.WORD,
            relief='flat',
            borderwidth=1
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure text tags for colored logging
        self.log_text.tag_configure("info", foreground=self.themes[self.current_theme]["info"])
        self.log_text.tag_configure("success", foreground=self.themes[self.current_theme]["success"])
        self.log_text.tag_configure("warning", foreground=self.themes[self.current_theme]["warning"])
        self.log_text.tag_configure("error", foreground=self.themes[self.current_theme]["warning"])
        self.log_text.tag_configure("timestamp", foreground=self.themes[self.current_theme]["fg"],
                                   font=('Consolas', 8))

        self.log("🚀 تم تشغيل واجهة التحكم العصرية", "success")
        
    def log(self, message, level="info"):
        """Add message to log with timestamp and color coding"""
        timestamp = time.strftime("%H:%M:%S")

        # Insert timestamp
        self.log_text.insert(tk.END, f"[{timestamp}] ", "timestamp")

        # Insert message with appropriate color
        self.log_text.insert(tk.END, f"{message}\n", level)

        # Auto-scroll if enabled
        if hasattr(self, 'auto_scroll_var') and self.auto_scroll_var.get():
            self.log_text.see(tk.END)

    def clear_log(self):
        """Clear the log"""
        self.log_text.delete(1.0, tk.END)
        self.log("📝 تم مسح السجل", "info")

    def refresh_ports(self):
        """Refresh available serial ports"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports
        if ports and not self.port_var.get():
            self.port_var.set(ports[0])

    def toggle_connection(self):
        """Toggle serial connection"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self):
        """Connect to Arduino with modern feedback"""
        try:
            port = self.port_var.get()
            baud = int(self.baud_var.get())

            if not port:
                messagebox.showerror("خطأ", "يرجى اختيار منفذ")
                return

            self.log(f"🔄 محاولة الاتصال بـ {port} بسرعة {baud}...", "info")

            self.serial_connection = serial.Serial(port, baud, timeout=1)
            time.sleep(2)  # Wait for Arduino to initialize

            self.is_connected = True
            self.connect_btn.config(text="🔌 قطع الاتصال")
            self.update_connection_indicator()
            self.log(f"✅ تم الاتصال بنجاح بـ {port}", "success")

            # Get initial status
            self.manual_refresh()

        except Exception as e:
            messagebox.showerror("خطأ في الاتصال", f"فشل الاتصال: {str(e)}")
            self.log(f"❌ فشل الاتصال: {str(e)}", "error")

    def disconnect(self):
        """Disconnect from Arduino with modern feedback"""
        if self.serial_connection:
            self.serial_connection.close()
            self.serial_connection = None

        self.is_connected = False
        self.connect_btn.config(text="🔗 اتصال")
        self.update_connection_indicator()
        self.log("🔌 تم قطع الاتصال", "warning")

    def send_command(self, command):
        """Send command to Arduino and return response with modern logging"""
        if not self.is_connected or not self.serial_connection:
            self.log(f"⚠️ خطأ: غير متصل - {command}", "warning")
            return None

        try:
            self.serial_connection.write((command + '\n').encode())
            time.sleep(0.1)  # Small delay for Arduino to process

            response = ""
            start_time = time.time()
            while time.time() - start_time < 2:  # 2 second timeout
                if self.serial_connection.in_waiting:
                    response = self.serial_connection.readline().decode().strip()
                    break
                time.sleep(0.01)

            # Color-coded logging based on response
            if response:
                if "ERROR" in response:
                    self.log(f"📤 {command} → ❌ {response}", "error")
                elif "OK" in response or "COMPLETE" in response or "STARTED" in response or "STOPPED" in response:
                    self.log(f"📤 {command} → ✅ {response}", "success")
                else:
                    self.log(f"📤 {command} → 📥 {response}", "info")
            else:
                self.log(f"📤 {command} → ⏰ لا توجد استجابة", "warning")

            return response

        except Exception as e:
            self.log(f"❌ خطأ في الإرسال: {str(e)}", "error")
            return None

    def on_pwm_change(self, channel, value):
        """Handle PWM value change with visual feedback"""
        value = int(float(value))
        self.pwm_values[channel] = value

        # Update display
        voltage = (value / 255.0) * 5.0
        self.pwm_labels[channel].config(text=f"{value} ({voltage:.2f}V)")

        # Update color preview with intensity
        self.update_pwm_color_preview(channel, value)

        # Send to Arduino
        self.send_command(f"SET_PWM,{channel},{value}")

    def update_pwm_color_preview(self, channel, value):
        """Update color preview canvas based on PWM value"""
        if hasattr(self, 'pwm_canvases') and channel < len(self.pwm_canvases):
            canvas = self.pwm_canvases[channel]

            # Calculate color intensity
            intensity = value / 255.0

            # Base colors for each channel
            base_colors = [
                (255, 71, 87),   # Red
                (55, 66, 250),   # Blue
                (46, 213, 115)   # Green
            ]

            r, g, b = base_colors[channel]
            # Apply intensity
            r = int(r * intensity)
            g = int(g * intensity)
            b = int(b * intensity)

            # Convert to hex
            color = f"#{r:02x}{g:02x}{b:02x}"

            # Update canvas
            canvas.delete("all")
            canvas.create_rectangle(2, 2, 58, 58, fill=color, outline="")

            # Add intensity text overlay
            if intensity > 0.5:
                text_color = "white"
            else:
                text_color = "black"
            canvas.create_text(30, 30, text=f"{value}", fill=text_color,
                             font=('Segoe UI', 10, 'bold'))

    def set_pwm_value(self, channel, value):
        """Set PWM value directly"""
        self.pwm_vars[channel].set(value)
        self.on_pwm_change(channel, value)

    def update_pwm_display(self):
        """Update PWM display values"""
        for i in range(3):
            value = self.pwm_values[i]
            voltage = (value / 255.0) * 5.0
            self.pwm_labels[i].config(text=f"{value} ({voltage:.2f}V)")
            if hasattr(self, 'pwm_canvases'):
                self.update_pwm_color_preview(i, value)

    def single_shoot(self):
        """Send single pulse with visual feedback"""
        self.send_command("SINGLE_SHOOT")
        # Flash visual indicator
        self.flash_pulse_indicator()

    def on_rate_change(self, value):
        """Handle shooting rate change"""
        self.shoot_rate = int(float(value))
        self.rate_label.config(text=f"{self.shoot_rate} Hz")
        self.send_command(f"SET_RATE,{self.shoot_rate}")
        # Update status if shooting is active
        if self.continuous_shooting:
            self.update_shooting_indicator()

    def start_continuous_shooting(self):
        """Start continuous shooting with visual feedback"""
        response = self.send_command("START_SHOOT")
        if response and "STARTED" in response:
            self.continuous_shooting = True
            self.update_shooting_indicator()
            # Disable start button, enable stop button
            self.start_shoot_btn.config(state='disabled')
            self.stop_shoot_btn.config(state='normal')

    def stop_continuous_shooting(self):
        """Stop continuous shooting with visual feedback"""
        response = self.send_command("STOP_SHOOT")
        if response and "STOPPED" in response:
            self.continuous_shooting = False
            self.update_shooting_indicator()
            # Enable start button, disable stop button
            self.start_shoot_btn.config(state='normal')
            self.stop_shoot_btn.config(state='disabled')

    def goto_angle(self):
        """Move stepper to specific angle"""
        angle = self.angle_var.get()
        if 0 <= angle < 360:
            response = self.send_command(f"STEPPER_ANGLE,{angle}")
        else:
            messagebox.showerror("خطأ", "الزاوية يجب أن تكون بين 0 و 359")

    def on_speed_change(self, value):
        """Handle stepper speed change"""
        speed = int(float(value))
        self.stepper_data["speed"] = speed
        self.speed_label.config(text=f"{speed} RPM")
        response = self.send_command(f"STEPPER_SPEED,{speed}")

    def stepper_cw(self):
        """Start clockwise rotation"""
        response = self.send_command("STEPPER_CW")

    def stepper_ccw(self):
        """Start counter-clockwise rotation"""
        response = self.send_command("STEPPER_CCW")

    def stepper_stop(self):
        """Stop stepper motor"""
        response = self.send_command("STEPPER_STOP")

    def stepper_reset(self):
        """Reset stepper position"""
        response = self.send_command("STEPPER_RESET")

    def relay_on(self, relay_id):
        """Turn relay on"""
        timer_var = getattr(self, f"timer_{relay_id.lower()}_var")
        timer = timer_var.get()
        response = self.send_command(f"RELAY_{relay_id}_ON,{timer}")

    def relay_off(self, relay_id):
        """Turn relay off"""
        response = self.send_command(f"RELAY_{relay_id}_OFF")

    def manual_refresh(self):
        """Manually refresh all status"""
        if not self.is_connected:
            return

        # Get PWM values
        response = self.send_command("GET_PWM")
        if response and "PWM_VALUES:" in response:
            values = response.split(":")[1].split(",")
            for i, val in enumerate(values[:3]):
                self.pwm_values[i] = int(val)
                self.pwm_vars[i].set(int(val))
            self.update_pwm_display()

        # Get stepper status
        response = self.send_command("GET_STEPPER")
        if response and "STEPPER_STATUS:" in response:
            data = response.split(":")[1].split(",")
            if len(data) >= 3:
                self.stepper_data["angle"] = float(data[0])
                self.stepper_data["speed"] = int(data[1])
                self.stepper_data["mode"] = data[2]
                self.stepper_status_label.config(
                    text=f"الزاوية: {self.stepper_data['angle']:.1f}° | "
                         f"السرعة: {self.stepper_data['speed']} RPM | "
                         f"الوضع: {self.stepper_data['mode']}")

        # Get relay status
        response = self.send_command("GET_RELAY")
        if response and "RELAY_STATUS:" in response:
            data = response.split(":")[1].split(",")
            if len(data) >= 6:
                # Right relay
                self.relay_data["right"]["active"] = data[0] == "1"
                self.relay_data["right"]["timer"] = int(data[1])
                self.relay_data["right"]["remaining"] = int(data[2])

                # Left relay
                self.relay_data["left"]["active"] = data[3] == "1"
                self.relay_data["left"]["timer"] = int(data[4])
                self.relay_data["left"]["remaining"] = int(data[5])

                # Update display
                self.update_relay_display()

    def update_relay_display(self):
        """Update relay status display"""
        for relay_id in ["right", "left"]:
            data = self.relay_data[relay_id]
            status_label = getattr(self, f"relay_{relay_id}_status")

            if data["active"]:
                if data["remaining"] > 0:
                    status_text = f"الحالة: نشط (متبقي: {data['remaining']/1000:.1f}s)"
                else:
                    status_text = "الحالة: نشط (يدوي)"
            else:
                status_text = "الحالة: متوقف"

            status_label.config(text=status_text)

    def toggle_auto_refresh(self):
        """Toggle auto refresh"""
        self.auto_refresh = self.auto_refresh_var.get()

    def auto_refresh_timer(self):
        """Auto refresh timer"""
        if self.auto_refresh and self.is_connected:
            self.manual_refresh()
        self.root.after(2000, self.auto_refresh_timer)  # Every 2 seconds

    def save_arduino_settings(self):
        """Save settings to Arduino EEPROM"""
        response = self.send_command("SAVE")
        if response and "SAVED" in response:
            messagebox.showinfo("نجح", "تم حفظ الإعدادات في Arduino")
        else:
            messagebox.showerror("خطأ", "فشل في حفظ الإعدادات")

    def reset_arduino_settings(self):
        """Reset Arduino to default settings"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟"):
            response = self.send_command("RESET")
            if response and "COMPLETE" in response:
                messagebox.showinfo("نجح", "تم إعادة تعيين الإعدادات")
                self.manual_refresh()
            else:
                messagebox.showerror("خطأ", "فشل في إعادة التعيين")

    def load_settings(self):
        """Load GUI settings from file"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r') as f:
                    settings = json.load(f)
                    self.auto_refresh = settings.get('auto_refresh', False)
        except Exception as e:
            self.auto_refresh = False

    def save_settings(self):
        """Save GUI settings to file"""
        try:
            settings = {
                'auto_refresh': self.auto_refresh
            }
            with open(self.settings_file, 'w') as f:
                json.dump(settings, f)
        except Exception as e:
            pass

    def on_closing(self):
        """Handle window closing"""
        self.save_settings()
        if self.is_connected:
            self.disconnect()
        self.root.destroy()

def main():
    root = tk.Tk()
    app = ModernArduinoController(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()

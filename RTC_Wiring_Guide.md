# دليل توصيل وحدة RTC للجدولة الذكية

## المكونات المطلوبة

### وحدة RTC
- **DS3231**: الأفضل (دقة عالية، مقاومة للحرارة)
- **DS1307**: بديل اقتصادي (يحتاج بطارية خارجية)

### البطارية
- **DS3231**: بطارية مدمجة CR2032
- **DS1307**: بطارية خارجية CR2032

## مخطط التوصيل

### Arduino Uno + DS3231/DS1307

```
Ard<PERSON>o Uno    →    RTC Module
─────────────────────────────────
5V             →    VCC
GND            →    GND
A4 (SDA)       →    SDA
A5 (SCL)       →    SCL
```

### Arduino Nano + DS3231/DS1307

```
Arduin<PERSON>   →    RTC Module
─────────────────────────────────
5V             →    <PERSON><PERSON>
GND            →    GND
A4 (SDA)       →    SDA
A5 (SCL)       →    SCL
```

### Arduino Mega + DS3231/DS1307

```
Arduino Mega   →    RTC Module
─────────────────────────────────
5V             →    VCC
GND            →    GND
20 (SDA)       →    SDA
21 (SCL)       →    SCL
```

## التوصيل التفصيلي

### الخطوة 1: إعداد وحدة RTC
1. تأكد من وجود البطارية في وحدة RTC
2. للـ DS3231: البطارية مدمجة عادة
3. للـ DS1307: أدخل بطارية CR2032

### الخطوة 2: توصيل الأسلاك
1. **الطاقة**:
   - VCC من RTC إلى 5V في Arduino
   - GND من RTC إلى GND في Arduino

2. **الاتصال I2C**:
   - SDA من RTC إلى A4 في Arduino Uno/Nano
   - SCL من RTC إلى A5 في Arduino Uno/Nano

### الخطوة 3: التحقق من التوصيل
```cpp
// كود اختبار بسيط
#include <Wire.h>

void setup() {
  Serial.begin(9600);
  Wire.begin();
  
  Wire.beginTransmission(0x68); // عنوان DS3231/DS1307
  if (Wire.endTransmission() == 0) {
    Serial.println("RTC found!");
  } else {
    Serial.println("RTC not found!");
  }
}

void loop() {
  // فارغ
}
```

## عناوين I2C

### العناوين الافتراضية:
- **DS3231**: 0x68
- **DS1307**: 0x68
- **LCD**: 0x27 (أو 0x3F)

### فحص العناوين:
```cpp
// مسح عناوين I2C
#include <Wire.h>

void setup() {
  Wire.begin();
  Serial.begin(9600);
  Serial.println("Scanning I2C addresses...");
  
  for(byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    if(Wire.endTransmission() == 0) {
      Serial.print("Device found at address 0x");
      if(address < 16) Serial.print("0");
      Serial.println(address, HEX);
    }
  }
}

void loop() {}
```

## استكشاف الأخطاء

### مشكلة: "RTC Module Error"
**الأسباب المحتملة:**
1. توصيل خاطئ للأسلاك
2. وحدة RTC معطلة
3. عنوان I2C خاطئ

**الحلول:**
1. تحقق من توصيل VCC, GND, SDA, SCL
2. استخدم مقياس متعدد لفحص الجهد
3. جرب وحدة RTC أخرى

### مشكلة: "RTC Lost Power"
**الأسباب المحتملة:**
1. البطارية فارغة
2. البطارية غير موصولة بشكل صحيح
3. وحدة RTC جديدة

**الحلول:**
1. استبدل البطارية CR2032
2. تأكد من تركيب البطارية بالاتجاه الصحيح
3. انتظر حتى يتم ضبط الوقت تلقائياً

### مشكلة: الوقت غير صحيح
**الأسباب المحتملة:**
1. لم يتم ضبط الوقت الأولي
2. البطارية ضعيفة
3. تداخل في إشارة I2C

**الحلول:**
1. ارفع الكود مرة أخرى لضبط الوقت
2. استبدل البطارية
3. استخدم مقاومات pull-up (4.7kΩ)

## نصائح مهمة

### للحصول على أفضل أداء:
1. **استخدم DS3231**: أكثر دقة من DS1307
2. **بطارية جيدة**: استخدم بطارية عالية الجودة
3. **أسلاك قصيرة**: قلل طول أسلاك I2C
4. **مقاومات Pull-up**: أضف 4.7kΩ إذا لزم الأمر

### صيانة دورية:
1. **فحص البطارية**: كل 2-3 سنوات
2. **تنظيف التوصيلات**: إزالة الأكسدة
3. **فحص الوقت**: مقارنة مع الوقت الفعلي شهرياً

## مخطط الدائرة الكاملة

```
                    Arduino Uno
                   ┌─────────────┐
                   │             │
    ┌──────────────┤ 5V      A4  ├──────────┐
    │              │         A5  ├────────┐ │
    │              │             │        │ │
    │              │         GND ├──────┐ │ │
    │              └─────────────┘      │ │ │
    │                                   │ │ │
    │              DS3231 RTC           │ │ │
    │             ┌─────────────┐       │ │ │
    └─────────────┤ VCC     SDA ├───────┘ │ │
                  │         SCL ├─────────┘ │
                  │             │           │
                  │         GND ├───────────┘
                  └─────────────┘
```

## الخلاصة
- التوصيل بسيط عبر 4 أسلاك فقط
- تأكد من البطارية والتوصيلات
- استخدم DS3231 للحصول على أفضل دقة
- النظام سيعمل تلقائياً بعد التوصيل الصحيح

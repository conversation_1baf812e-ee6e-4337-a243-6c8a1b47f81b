# 🚀 Arduino Controller - KivyMD Modern GUI
# واجهة Arduino العصرية - KivyMD

## 📱 واجهة عصرية بتصميم Material Design
واجهة تحكم أنيقة وحديثة لـ Arduino Uno R4 WiFi باستخدام KivyMD مع تصميم Material Design متجاوب يعمل على جميع الشاشات.

## 📁 الملفات الأساسية
- **`src/arduino_controller.cpp`** - كود Arduino
- **`arduino_kivymd_gui.py`** - الواجهة العصرية KivyMD (الأساسية)
- **`arduino_gui.py`** - الواجهة التقليدية Tkinter (احتياطية)
- **`run_arduino_gui.py`** - مشغل ذكي (يختار الواجهة المناسبة)
- **`requirements.txt`** - المكتبات: pyserial, kivy, kivymd

## ✨ المميزات العصرية الجديدة

### 📱 **KivyMD Material Design**
- **تصميم Material Design**: واجهة عصرية وأنيقة
- **متجاوب تماماً**: يتكيف مع جميع أحجام الشاشات
- **رسوم متحركة سلسة**: انتقالات وتأثيرات بصرية
- **أداء محسن**: أسرع وأكثر استجابة

### 🎨 **واجهة متطورة**
- **بطاقات عصرية**: تنظيم أنيق للمحتوى
- **أزرار عائمة**: Material Design buttons
- **شرائح تمرير متقدمة**: تحكم دقيق وسلس
- **إشعارات ذكية**: Snackbars وحوارات تفاعلية

### 🌓 **ثيمات Material**
- **وضع داكن** 🌙: Material Dark Theme
- **وضع فاتح** ☀️: Material Light Theme
- **ألوان ديناميكية**: Teal & Orange palette
- **تبديل فوري**: بنقرة واحدة

### 🎛️ **تحكم متقدم**
- **تبويبات عصرية**: Material Design Tabs
- **شرائح PWM**: تحكم دقيق مع معاينة فورية
- **أزرار سريعة**: قيم محددة مسبقاً (0%, 25%, 50%, 75%, 100%)
- **مؤشرات حالة**: Spinners وإشعارات مرئية

### 📊 **سجل تفاعلي**
- **قائمة ديناميكية**: Material Design List
- **تصنيف ملون**: أيقونات وألوان للرسائل
- **تمرير تلقائي**: متابعة الأحداث الجديدة
- **مسح سريع**: زر مسح بأيقونة

### 🔧 **الوظائف الكاملة**
- **🎨 PWM**: تحكم RGB متقدم (D9,D6,D5)
- **⚡ النبضات**: واحدة أو مستمرة مع تحكم بالمعدل (D8)
- **🔄 المحرك المتدرج**: تحكم بالزاوية والسرعة (D2-D7)
- **🔌 المرحلات**: مؤقت تلقائي ذكي (D10,D11)
- **💾 الإعدادات**: حفظ في EEPROM + إعدادات الواجهة

## ⚡ التشغيل السريع

### 🔧 متطلبات الأجهزة
```
Arduino Uno R4 WiFi
- RGB LEDs على المنافذ D9, D6, D5
- محرك متدرج 28BYJ-48 على D2, D3, D4, D7
- مرحلان على D10, D11
- منفذ نبضات على D8
```

### 💻 متطلبات البرمجيات
- **Python 3.7+** (مع دعم Kivy)
- **KivyMD** (واجهة Material Design)
- **PySerial** (اتصال السيريال)

### 🚀 خطوات التشغيل

#### 1. تثبيت المتطلبات
```bash
pip install pyserial kivy kivymd
```

**أو من ملف requirements.txt:**
```bash
pip install -r requirements.txt
```

#### 2. رفع كود Arduino
```cpp
// رفع src/arduino_controller.cpp إلى Arduino Uno R4
```

#### 3. تشغيل الواجهة
```bash
# الطريقة الأسهل (يختار الواجهة المناسبة تلقائياً)
python run_arduino_gui.py

# أو مباشرة KivyMD
python arduino_kivymd_gui.py

# أو Windows
run.bat

# أو Linux/Mac
chmod +x run.sh && ./run.sh
```

### 🎯 الاستخدام
1. **اختر المنفذ** من القائمة المنسدلة 📍
2. **اضبط السرعة** (115200 افتراضي) ⚡
3. **اضغط اتصال** 🔗
4. **استمتع بالواجهة العصرية** 🎮
5. **بدل الثيم** بنقرة واحدة 🌙☀️

## 🎮 طريقة الاستخدام

### 1️⃣ **الاتصال**
- اختر المنفذ من القائمة 📍
- اضبط السرعة (115200) ⚡
- اضغط "🔗 اتصال"

### 2️⃣ **التحكم**
- **🎨 تبويب PWM**: تحكم في الألوان
- **⚡ تبويب النبضات**: إرسال نبضات
- **🔄 تبويب المحرك**: تحريك المحرك
- **🔌 تبويب المرحلات**: تشغيل المرحلات

### 3️⃣ **المراقبة**
- **📊 السجل**: متابعة جميع العمليات
- **🔄 التحديث التلقائي**: كل ثانيتين
- **💾 الحفظ**: إعدادات دائمة

## 🛠️ استكشاف الأخطاء

### ❌ **مشاكل شائعة**
```bash
# مشكلة: مكتبات مفقودة
pip install pyserial kivy kivymd

# مشكلة: Kivy لا يعمل على Windows
pip install kivy[base,media] --pre --extra-index-url https://kivy.org/downloads/simple/

# مشكلة: المنفذ مشغول
# أغلق أي برنامج آخر يستخدم Arduino

# مشكلة: لا توجد استجابة
# تأكد من رفع الكود الصحيح للـ Arduino

# مشكلة: الواجهة لا تظهر
# تأكد من تثبيت جميع المتطلبات
# جرب الواجهة التقليدية: python arduino_gui.py
```

## 🆚 **مقارنة الواجهات**

| الميزة | KivyMD (الجديدة) | Tkinter (التقليدية) |
|--------|------------------|---------------------|
| التصميم | Material Design عصري | تقليدي بسيط |
| الاستجابة | متجاوب تماماً | محدود |
| الرسوم المتحركة | سلسة ومتقدمة | أساسية |
| الأداء | محسن وسريع | جيد |
| حجم الملف | أكبر (مكتبات إضافية) | أصغر |
| التوافق | يحتاج تثبيت | مدمج مع Python |

## 🎯 **التوصية**
- **للاستخدام العادي**: KivyMD (أجمل وأكثر عصرية)
- **للأنظمة المحدودة**: Tkinter (أبسط وأخف)
- **المشغل الذكي** يختار الأنسب تلقائياً!

---
## 👥 **المطورون**
**© HS TEAM** - Arduino Controller Project

🎉 **استمتع بالواجهة العصرية الجديدة!**
📱 **Material Design في أبهى صوره!**
